process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Utility: Serialize any array of strings/objects as { type: 'text', text: string }[] for MCP compliance
function makeTextContentArray(contentArr: any[]): { type: 'text', text: string }[] {
  return contentArr.map(entry => {
    if (typeof entry === "string") {
      return { type: 'text', text: entry };
    }
    if (entry && typeof entry === "object" && entry.type === "text" && typeof entry.text === "string") {
      // Already compliant
      return entry;
    }
    // For any other objects/values, serialize as prettified JSON
    return { type: 'text', text: JSON.stringify(entry, null, 2) };
  });
}
// File: game-state-server/src/index.ts

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js';
import { formatSheetByGameLine } from './characterSheets.js';
import { GameDatabase, type AntagonistRow } from './db.js';

console.log("Initializing server...");
const server = new Server({ name: 'rpg-game-state-server', version: '2.1.0' }, { capabilities: { tools: {} } });
console.log("Server initialized.");

console.log("Initializing database...");
const db = new GameDatabase();
console.log("Database initialized.");

console.log("About to define toolDefinitions...");
const toolDefinitions: any[] = [
  {
    name: 'create_character',
    description: 'Create a new oWoD character.',
    inputSchema: {
      type: 'object',
      properties: {
        // Core character properties
        name: { type: 'string', description: 'Character name' },
        concept: { type: 'string', description: 'Character concept', nullable: true },
        game_line: { type: 'string', enum: ['vampire', 'werewolf', 'mage', 'changeling'], description: 'Game line/splat' },
        // Attributes and basic traits are filled in backend with defaults or can be optionally included
        // --- Vampire-specific fields
        clan: { type: 'string', description: 'Vampire clan (e.g., Brujah, Malkavian)', nullable: true },
        generation: { type: 'number', description: 'Vampire generation', nullable: true },
        blood_pool_current: { type: 'number', description: 'Current Blood Pool', nullable: true },
        blood_pool_max: { type: 'number', description: 'Max Blood Pool', nullable: true },
        humanity: { type: 'number', description: 'Humanity (Vampire only)', nullable: true },
        // --- Werewolf-specific fields
        breed: { type: 'string', description: 'Werewolf breed (e.g., Homid, Metis, Lupus)', nullable: true },
        auspice: { type: 'string', description: 'Werewolf auspice (e.g., Ragabash, Theurge)', nullable: true },
        tribe: { type: 'string', description: 'Werewolf tribe', nullable: true },
        gnosis_current: { type: 'number', description: 'Current Gnosis', nullable: true },
        gnosis_permanent: { type: 'number', description: 'Permanent Gnosis', nullable: true },
        rage_current: { type: 'number', description: 'Current Rage', nullable: true },
        rage_permanent: { type: 'number', description: 'Permanent Rage', nullable: true },
        renown_glory: { type: 'number', description: 'Glory Renown', nullable: true },
        renown_honor: { type: 'number', description: 'Honor Renown', nullable: true },
        renown_wisdom: { type: 'number', description: 'Wisdom Renown', nullable: true },
        // --- Mage-specific fields
        tradition_convention: { type: 'string', description: 'Mage tradition or Convention', nullable: true },
        arete: { type: 'number', description: 'Mage Arete', nullable: true },
        quintessence: { type: 'number', description: 'Mage Quintessence', nullable: true },
        paradox: { type: 'number', description: 'Mage Paradox', nullable: true },
        // --- Changeling-specific fields
        kith: { type: 'string', description: 'Changeling kith', nullable: true },
        seeming: { type: 'string', description: 'Changeling seeming', nullable: true },
        glamour_current: { type: 'number', description: 'Current Glamour', nullable: true },
        glamour_permanent: { type: 'number', description: 'Permanent Glamour', nullable: true },
        banality_permanent: { type: 'number', description: 'Permanent Banality', nullable: true },

        // Optionally add abilities, disciplines, spheres, arts, realms, etc.
        abilities: { type: 'array', items: { type: 'object' }, nullable: true, description: 'Starting abilities for the character' },
        disciplines: { type: 'array', items: { type: 'object' }, nullable: true, description: 'Starting disciplines (Vampire only)' },
        spheres: { type: 'array', items: { type: 'object' }, nullable: true, description: 'Spheres (Mage only)' },
        arts: { type: 'array', items: { type: 'object' }, nullable: true, description: 'Changeling Arts' },
        realms: { type: 'array', items: { type: 'object' }, nullable: true, description: 'Changeling Realms' }
      },
      required: ['name', 'game_line']
    }
  },
  {
    name: 'get_character',
    description: 'Retrieve full character data.',
    inputSchema: {
      type: 'object',
      properties: { character_id: { type: 'number' } },
      required: ['character_id']
    }
  },
  {
    name: 'get_character_by_name',
    description: 'Retrieve character by name.',
    inputSchema: {
      type: 'object',
      properties: { name: { type: 'string' } },
      required: ['name']
    }
  },
  {
    name: 'update_character',
    description: 'Update character traits.',
    inputSchema: {
      type: 'object',
      properties: {
        character_id: { type: 'number' },
        updates: { type: 'object' }
      },
      required: ['character_id', 'updates']
    }
  },
  {
    name: 'spend_resource',
    description: 'Spend a character resource.',
    inputSchema: {
      type: 'object',
      properties: {
        character_id: { type: 'number' },
        resource_name: { type: 'string', enum: ['willpower', 'blood', 'gnosis', 'rage', 'glamour', 'quintessence', 'paradox'] },
        amount: { type: 'number', default: 1 }
      },
      required: ['character_id', 'resource_name']
    }
  },
  {
    name: "restore_resource",
    description: "Restore a character resource like Willpower, Blood, etc.",
    inputSchema: {
      type: "object",
      properties: {
        character_id: { type: "number" },
        resource_name: { type: "string", enum: ['willpower', 'blood', 'gnosis', 'rage', 'glamour', 'quintessence'] },
        amount: { type: 'number', default: 1 }
      },
      required: ['character_id', 'resource_name']
    }
  },
  {
    name: 'apply_damage',
    description: 'Apply health level damage to a target after a successful damage roll.',
    inputSchema: {
      type: 'object',
      properties: {
        target_type: { type: 'string', enum: ['character', 'npc'] },
        target_id: { type: 'number' },
        damage_successes: { type: 'number', description: 'The number of successes from the damage roll.' },
        damage_type: { type: 'string', enum: ['bashing', 'lethal', 'aggravated'], default: 'lethal' }
      },
      required: ['target_type', 'target_id', 'damage_successes', 'damage_type']
    }
  },
  {
    name: 'create_antagonist',
    description: 'Create an antagonist from a template.',
    inputSchema: {
      type: 'object',
      properties: {
        template_name: { type: 'string' },
        custom_name: { type: 'string' }
      },
      required: ['template_name']
    }
  },
  {
    name: 'get_antagonist',
    description: 'Retrieve antagonist data by ID.',
    inputSchema: {
      type: 'object',
      properties: { npc_id: { type: 'number' } },
      required: ['npc_id']
    }
  },
{
  name: 'gain_resource',
  description: 'Gain a resource through an in-game action (e.g., feeding, meditation, quest). Applies game-line–specific logic.',
  inputSchema: {
    type: 'object',
    properties: {
      character_id: { type: 'number' },
      resource_name: { type: 'string', enum: ['willpower', 'blood', 'gnosis', 'glamour', 'quintessence'] },
      roll_successes: { type: 'number', minimum: 1 }
    },
    required: ['character_id', 'resource_name', 'roll_successes']
  }
}
];

console.log("Initial toolDefinitions array created. Length:", toolDefinitions.length);

// --- Initiative tool definitions ---
toolDefinitions.push(
  {
    name: "set_initiative",
    description: "Set the initiative order for a scene. Overwrites all entries for that scene. Each entry may be a PC or NPC.",
    inputSchema: {
      type: "object",
      properties: {
        scene_id: { type: "string" },
        entries: {
          type: "array",
          items: {
            type: "object",
            properties: {
              character_id: { type: ["number", "null"] },
              npc_id: { type: ["number", "null"] },
              actor_name: { type: "string" },
              initiative_score: { type: "number" },
              turn_order: { type: "number" }
            },
            required: ["actor_name", "initiative_score", "turn_order"]
          }
        }
      },
      required: ["scene_id", "entries"]
    }
  },
  {
    name: "get_initiative_order",
    description: "Get current initiative order for the specified scene.",
    inputSchema: {
      type: "object",
      properties: {
        scene_id: { type: "string" }
      },
      required: ["scene_id"]
    }
  }
);
  // Combat Turn Management:
toolDefinitions.push(
  {
    name: 'advance_turn',
    description: 'Advances to the next actor in the initiative order for a scene.',
    inputSchema: {
      type: 'object',
      properties: { scene_id: { type: 'string' } },
      required: ['scene_id']
    }
  },
  {
    name: 'get_current_turn',
    description: 'Retrieve the current actor and round info for a combat scene.',
    inputSchema: {
      type: 'object',
      properties: { scene_id: { type: 'string' } },
      required: ['scene_id']
    }
  }
);
// XP management tools:
toolDefinitions.push(
  {
    name: 'award_xp',
    description: 'Award experience points to a character.',
    inputSchema: {
      type: 'object',
      properties: {
        character_id: { type: 'number' },
        amount: { type: 'number', minimum: 1 },
        reason: { type: 'string' }
      },
      required: ['character_id', 'amount', 'reason']
    }
  },
  {
    name: 'spend_xp',
    description: 'Spend a character\'s XP to improve a trait (logging only; does not yet update trait).',
    inputSchema: {
      type: 'object',
      properties: {
        character_id: { type: 'number' },
        amount: { type: 'number', minimum: 1 },
        reason: { type: 'string' },
        trait_name: { type: 'string' },
        trait_info: { type: 'object' }
      },
      required: ['character_id', 'amount', 'reason']
    }
  }
);
// -- Add improve_trait tool schema
toolDefinitions.push({
  name: 'improve_trait',
  description: 'Increase a trait for a character by spending XP according to oWoD rules. Computes XP cost, checks XP, applies change and deduction. Supported trait_types: attribute, ability, discipline, sphere, art, realm, willpower, power_stat. trait_name must match the trait/facet name, e.g. "strength" or "Firearms".',
  inputSchema: {
    type: 'object',
    properties: {
      character_id: { type: 'number' },
      trait_type: {
        type: 'string',
        enum: ['attribute', 'ability', 'discipline', 'sphere', 'art', 'realm', 'willpower', 'power_stat']
      },
      trait_name: { type: 'string' }
    },
    required: ['character_id', 'trait_type', 'trait_name']
  }
});
/**
 * Calculates the XP cost to improve a character trait to the next level.
 */
toolDefinitions.push({
  name: 'get_trait_improvement_cost',
  description: 'Calculates the XP cost to improve a character trait to the next level.',
  inputSchema: {
    type: 'object',
    properties: {
      character_id: { type: 'number' },
      trait_type: {
        type: 'string',
        enum: ['attribute', 'ability', 'discipline', 'sphere', 'art', 'realm', 'willpower', 'power_stat']
      },
      trait_name: { type: 'string' }
    },
    required: ['character_id', 'trait_type', 'trait_name']
  }
});

console.log("toolDefinitions completed. Length:", toolDefinitions.length);
console.log("First few tools:", toolDefinitions.slice(0, 3).map(t => t.name));

// Register MCP handlers
console.log("About to register handlers - this should appear in output");

export async function handleToolRequest(request: any) {
  const { name, arguments: args } = request.params;
  console.log(`Handling tool request: ${name}`);
  try {
    console.log(`Inside try block, before switch statement: ${name}`);
    switch (name) {

    case 'create_character': {
      // Input validation
      if (!args.name || typeof args.name !== 'string' || args.name.trim() === '') {
        return { content: [{ type: 'text', text: 'Error: Missing required field: name.' }], isError: true };
      }

      if (!args.game_line || typeof args.game_line !== 'string') {
        return { content: [{ type: 'text', text: 'Error: Missing required field: game_line.' }], isError: true };
      }

      if (!['vampire', 'werewolf', 'mage', 'changeling'].includes(args.game_line)) {
        return { content: [{ type: 'text', text: 'Error: Invalid value for game_line.' }], isError: true };
      }

      try {
        const char = db.createCharacter(args);
        if (!char) throw new Error("Character creation failed in database.");
        const sheet = formatSheetByGameLine({ character: char });
        return {
          content: [
            { type: 'text', text: `✅ Character '${char.name}' created successfully! [ID: ${char.id}]` },
            sheet
          ]
        };
      } catch (error: any) {
        // Handle database constraint errors with user-friendly messages
        if (error.message && error.message.includes('UNIQUE constraint failed')) {
          return { content: [{ type: 'text', text: 'UNIQUE constraint failed error on second attempt.' }], isError: true };
        }
        return { content: [{ type: 'text', text: `❌ Error creating character: ${error.message}` }], isError: true };
      }
    }

    case 'get_character': {
      // Input validation
      if (args.character_id === undefined || args.character_id === null) {
        return { content: [{ type: 'text', text: '❌ Error: Missing required field: character_id' }], isError: true };
      }

      if (typeof args.character_id !== 'number' || !Number.isInteger(args.character_id) || args.character_id <= 0) {
        return { content: [{ type: 'text', text: '❌ Error: character_id must be a positive integer' }], isError: true };
      }

      const char = db.getCharacterById(args.character_id);
      if (!char) {
        return { content: [{ type: 'text', text: `❌ Character with ID ${args.character_id} not found.` }], isError: true };
      }
      const formattedSheet = formatSheetByGameLine({ character: char });
      return {
        content: [
          { type: 'text', text: formattedSheet.text },
          { type: 'text', text: JSON.stringify(char) }
        ]
      };
    }

    case 'get_character_by_name': {
      const char = db.getCharacterByName(args.name);
      if (!char) {
        return { content: [{ type: 'text', text: `❌ Character '${args.name}' not found.` }], isError: true };
      }
      const formattedSheet = formatSheetByGameLine({ character: char });
      return {
        content: [
          { type: 'text', text: formattedSheet.text },
          { type: 'text', text: JSON.stringify(char) }
        ]
      };
    }

    case 'update_character': {
      // Input validation
      if (args.character_id === undefined || args.character_id === null) {
        return { content: [{ type: 'text', text: '❌ Error: Missing required field: character_id' }], isError: true };
      }

      if (typeof args.character_id !== 'number' || !Number.isInteger(args.character_id) || args.character_id <= 0) {
        return { content: [{ type: 'text', text: '❌ Error: character_id must be a positive integer' }], isError: true };
      }

      if (!args.updates || typeof args.updates !== 'object' || Array.isArray(args.updates)) {
        return { content: [{ type: 'text', text: '❌ Error: updates must be a valid object' }], isError: true };
      }

      // Check if character exists
      const existingChar = db.getCharacterById(args.character_id);
      if (!existingChar) {
        return { content: [{ type: 'text', text: `❌ Character with ID ${args.character_id} not found.` }], isError: true };
      }

      // Validate field names and types
      const validFields = [
        'name', 'concept', 'game_line', 'strength', 'dexterity', 'stamina',
        'charisma', 'manipulation', 'appearance', 'perception', 'intelligence', 'wits',
        'willpower_current', 'willpower_permanent', 'health_levels', 'experience',
        // Vampire fields
        'clan', 'generation', 'blood_pool_current', 'blood_pool_max', 'humanity',
        // Werewolf fields
        'tribe', 'auspice', 'rage_current', 'rage_permanent', 'gnosis_current', 'gnosis_permanent',
        // Mage fields
        'tradition', 'essence', 'arete', 'quintessence', 'paradox',
        // Changeling fields
        'kith', 'court', 'glamour_current', 'glamour_permanent', 'banality'
      ];

      const invalidFields = Object.keys(args.updates).filter(field => !validFields.includes(field));
      if (invalidFields.length > 0) {
        return { content: [{ type: 'text', text: `❌ Error: Invalid field(s): ${invalidFields.join(', ')}` }], isError: true };
      }

      // Type validation for numeric fields
      const numericFields = [
        'strength', 'dexterity', 'stamina', 'charisma', 'manipulation', 'appearance',
        'perception', 'intelligence', 'wits', 'willpower_current', 'willpower_permanent',
        'experience', 'generation', 'blood_pool_current', 'blood_pool_max', 'humanity',
        'rage_current', 'rage_permanent', 'gnosis_current', 'gnosis_permanent',
        'arete', 'quintessence', 'paradox', 'glamour_current', 'glamour_permanent', 'banality'
      ];

      for (const [field, value] of Object.entries(args.updates)) {
        if (numericFields.includes(field) && value !== null && value !== undefined) {
          if (typeof value !== 'number' || !Number.isFinite(value)) {
            return { content: [{ type: 'text', text: `❌ Error: Field '${field}' must be a number, got ${typeof value}` }], isError: true };
          }
        }
        if (['name', 'concept', 'game_line', 'clan', 'tribe', 'auspice', 'tradition', 'essence', 'kith', 'court'].includes(field) && value !== null && value !== undefined) {
          if (typeof value !== 'string') {
            return { content: [{ type: 'text', text: `❌ Error: Field '${field}' must be a string, got ${typeof value}` }], isError: true };
          }
        }
      }

      try {
        // Use the improved updateCharacter that returns fresh data and prevents race conditions
        const updatedCharacter = db.updateCharacter(args.character_id, args.updates);

        if (!updatedCharacter) {
          return { content: [{ type: 'text', text: `❌ Error: Character ${args.character_id} not found or no valid updates applied` }], isError: true };
        }

        // Verify the update actually took effect by checking the returned fresh data
        let updateConfirmed = true;
        const failedUpdates: string[] = [];

        for (const [field, expectedValue] of Object.entries(args.updates)) {
          if (updatedCharacter[field] !== expectedValue) {
            updateConfirmed = false;
            failedUpdates.push(field);
          }
        }

        if (!updateConfirmed) {
          return { content: [{ type: 'text', text: `⚠️ Warning: Some updates may not have persisted. Failed fields: ${failedUpdates.join(', ')}` }], isError: false };
        }

        return { content: [{ type: 'text', text: `✅ Character ${args.character_id} updated successfully. All changes are immediately available.` }] };
      } catch (error: any) {
        return { content: [{ type: 'text', text: `❌ Error updating character: ${error.message}` }], isError: true };
      }
    }

    case 'spend_resource': {
      const { character_id, resource_name, amount = 1 } = args;
      const char = db.getCharacterById(character_id);
      if (!char) return { content: [{ type: 'text', text: '❌ Character not found!' }], isError: true };

      // Game line specific resource validation
      const gameLineResources: Record<string, string[]> = {
        vampire: ['willpower', 'blood'],
        werewolf: ['willpower', 'rage', 'gnosis'],
        mage: ['willpower', 'quintessence', 'paradox'],
        changeling: ['willpower', 'glamour']
      };

      const allowedResources = gameLineResources[char.game_line] || ['willpower'];
      if (!allowedResources.includes(resource_name)) {
        return { content: [{ type: 'text', text: `❌ Resource '${resource_name}' is not available for ${char.game_line} characters. Available resources: ${allowedResources.join(', ')}` }], isError: true };
      }

      // Use atomic resource operation to prevent race conditions and locking issues
      const result = db.atomicResourceOperation(character_id, resource_name, 'spend', amount);

      if (!result.success) {
        return { content: [{ type: 'text', text: `❌ ${result.error}` }], isError: true };
      }

      return { content: [{ type: 'text', text: `✅ ${char.name} spent ${amount} ${resource_name}. Remaining: ${result.newValue}` }] };
    }

    case 'restore_resource': {
      const { character_id, resource_name, amount = 1 } = args;
      const char = db.getCharacter(character_id);
      if (!char) return { content: [{ type: 'text', text: '❌ Character not found!' }], isError: true };

      // Resource mapping with max values for calculating limits
      const resourceMaxMap: Record<string, string> = {
        willpower: 'willpower_permanent',
        blood: 'blood_pool_max',
        gnosis: 'gnosis_permanent',
        rage: 'rage_permanent',
        glamour: 'glamour_permanent'
      };

      const maxColumn = resourceMaxMap[resource_name];
      const maxValue = maxColumn ? (char[maxColumn] || 0) : undefined;

      // Use atomic resource operation
      const result = db.atomicResourceOperation(character_id, resource_name, 'restore', amount, maxValue);

      if (!result.success) {
        return { content: [{ type: 'text', text: `❌ ${result.error}` }], isError: true };
      }

      // Calculate actual restored amount using the resource mapping
      const currentResourceField = resourceMaxMap[resource_name] ? `${resource_name}_current` : resource_name;
      const originalValue = char[currentResourceField] || 0;
      const actualRestored = result.newValue! - originalValue;
      const maxDisplay = maxValue ? `/${maxValue}` : '';

      return { content: [{ type: 'text', text: `✅ ${char.name} restored ${actualRestored} ${resource_name}. Current: ${result.newValue}${maxDisplay}` }] };
    }

    case 'gain_resource': {
      const { character_id, resource_name, roll_successes } = args;
      const char = db.getCharacter(character_id);
      if (!char) return { content: [{ type: 'text', text: '❌ Character not found!' }], isError: true };

      // Game line specific resource validation
      const gameLineResources: Record<string, string[]> = {
        vampire: ['willpower', 'blood'],
        werewolf: ['willpower', 'rage', 'gnosis'],
        mage: ['willpower', 'quintessence'],
        changeling: ['willpower', 'glamour']
      };

      const allowedResources = gameLineResources[char.game_line] || ['willpower'];
      if (!allowedResources.includes(resource_name)) {
        return { content: [{ type: 'text', text: `❌ Resource '${resource_name}' is not available for ${char.game_line} characters. Available resources: ${allowedResources.join(', ')}` }], isError: true };
      }

      // Game-line specific resource gain logic
      let gained = 0;
      let description = '';

      switch (char.game_line) {
        case 'vampire':
          if (resource_name === 'blood') {
            gained = roll_successes; // Each success = 1 blood point
            description = `feeding (${roll_successes} successes)`;
          } else if (resource_name === 'willpower') {
            gained = roll_successes >= 3 ? 1 : 0; // Need 3+ successes for willpower
            description = `meditation/rest (${roll_successes} successes)`;
          }
          break;
        case 'werewolf':
          if (resource_name === 'gnosis') {
            gained = Math.floor(roll_successes / 2); // 2 successes = 1 gnosis
            description = `spiritual communion (${roll_successes} successes)`;
          } else if (resource_name === 'rage') {
            gained = roll_successes; // Each success = 1 rage
            description = `fury/anger (${roll_successes} successes)`;
          } else if (resource_name === 'willpower') {
            gained = roll_successes >= 3 ? 1 : 0;
            description = `meditation/rest (${roll_successes} successes)`;
          }
          break;
        case 'mage':
          if (resource_name === 'quintessence') {
            gained = roll_successes; // Each success = 1 quintessence
            description = `tapping a Node (${roll_successes} successes)`;
          } else if (resource_name === 'willpower') {
            gained = roll_successes >= 3 ? 1 : 0;
            description = `meditation/rest (${roll_successes} successes)`;
          }
          break;
        case 'changeling':
          if (resource_name === 'glamour') {
            gained = roll_successes; // Each success = 1 glamour
            description = `inspiring creativity (${roll_successes} successes)`;
          } else if (resource_name === 'willpower') {
            gained = roll_successes >= 3 ? 1 : 0;
            description = `meditation/rest (${roll_successes} successes)`;
          }
          break;
      }

      if (gained === 0) {
        return { content: [{ type: 'text', text: `❌ Cannot gain ${resource_name} for ${char.game_line} or insufficient successes (need at least ${resource_name === 'willpower' ? '3' : '1'} success${resource_name === 'willpower' ? 'es' : ''}).` }], isError: true };
      }

      // Resource mapping for max values
      const resourceMaxMap: Record<string, string> = {
        willpower: 'willpower_permanent',
        blood: 'blood_pool_max',
        gnosis: 'gnosis_permanent',
        rage: 'rage_permanent',
        glamour: 'glamour_permanent'
      };

      const maxColumn = resourceMaxMap[resource_name];
      const maxValue = maxColumn ? (char[maxColumn] || 0) : undefined;

      // Check if character has resource pool configured
      if (maxColumn && maxValue === 0) {
        return { content: [{ type: 'text', text: `❌ Character ${char.name} does not have a ${resource_name} pool configured. Please set up the character's ${maxColumn} field first.` }], isError: true };
      }

      // Use atomic resource operation
      const result = db.atomicResourceOperation(character_id, resource_name, 'gain', gained, maxValue);

      if (!result.success) {
        return { content: [{ type: 'text', text: `❌ ${result.error}` }], isError: true };
      }

      // Calculate actual gained amount using the resource mapping
      const currentResourceField = resourceMaxMap[resource_name] ? `${resource_name}_current` : resource_name;
      const originalValue = char[currentResourceField] || 0;
      const actualGained = result.newValue! - originalValue;
      const maxDisplay = maxValue ? `/${maxValue}` : '';

      if (actualGained === 0) {
        return { content: [{ type: 'text', text: `❌ ${char.name} is already at maximum ${resource_name} (${originalValue}${maxDisplay})` }], isError: true };
      }

      return { content: [{ type: 'text', text: `✅ ${char.name} gained ${actualGained} ${resource_name} from ${description}. Current: ${result.newValue}${maxDisplay}` }] };
    }

    case 'apply_damage': {
      const { target_type, target_id, damage_successes, damage_type } = args;

      // Input validation
      if (!target_type || typeof target_type !== 'string') {
        return { content: [{ type: 'text', text: '❌ Error: Missing or invalid target_type' }], isError: true };
      }

      if (target_id === undefined || target_id === null) {
        return { content: [{ type: 'text', text: '❌ Error: Missing target_id' }], isError: true };
      }

      if (typeof target_id !== 'number' || !Number.isInteger(target_id) || target_id <= 0) {
        return { content: [{ type: 'text', text: `❌ Error: target_id must be a positive integer, got ${typeof target_id}: ${target_id}` }], isError: true };
      }

      if (!['character', 'npc'].includes(target_type)) {
        return { content: [{ type: 'text', text: `❌ Error: target_type must be 'character' or 'npc', got '${target_type}'` }], isError: true };
      }

      if (damage_successes === undefined || damage_successes === null) {
        return { content: [{ type: 'text', text: '❌ Error: Missing damage_successes' }], isError: true };
      }

      if (typeof damage_successes !== 'number' || !Number.isInteger(damage_successes) || damage_successes < 0) {
        return { content: [{ type: 'text', text: `❌ Error: damage_successes must be a non-negative integer, got ${typeof damage_successes}: ${damage_successes}` }], isError: true };
      }

      if (!damage_type || typeof damage_type !== 'string') {
        return { content: [{ type: 'text', text: '❌ Error: Missing or invalid damage_type' }], isError: true };
      }

      if (!['bashing', 'lethal', 'aggravated'].includes(damage_type)) {
        return { content: [{ type: 'text', text: `❌ Error: damage_type must be 'bashing', 'lethal', or 'aggravated', got '${damage_type}'` }], isError: true };
      }

      let target;
      if (target_type === 'character') {
        target = db.getCharacterById(target_id);
      } else if (target_type === 'npc') {
        target = db.getAntagonistById(target_id);
      }

      if (!target) {
        return { content: [{ type: 'text', text: `❌ ${target_type} with ID ${target_id} not found.` }], isError: true };
      }

      // Create damage object based on type and successes
      const damageObj: any = {
        bashing: damage_type === 'bashing' ? damage_successes : 0,
        lethal: damage_type === 'lethal' ? damage_successes : 0,
        aggravated: damage_type === 'aggravated' ? damage_successes : 0
      };

      const result = db.applyHealthLevelDamage(target_type, target_id, damageObj);

      if (result.success) {
        return {
          content: [
            { type: 'text', text: `💥 Applied ${damage_successes} ${damage_type} damage to ${target.name}` },
            { type: 'text', text: result.statusText },
            { type: 'text', text: `Wound Penalty: -${result.woundPenalty}` }
          ]
        };
      } else {
        return { content: [{ type: 'text', text: `❌ Failed to apply damage: ${result.message}` }], isError: true };
      }
    }

    case 'award_xp': {
      const { character_id, amount, reason } = args;

      // Validation
      if (amount <= 0) {
        return { content: [{ type: 'text', text: '❌ Amount must be positive.' }], isError: true };
      }

      const char = db.getCharacterById(character_id);
      if (!char) {
        return { content: [{ type: 'text', text: '❌ Character not found!' }], isError: true };
      }

      // Insert into ledger and update character atomically
      try {
        db.addExperienceLedgerEntry(character_id, amount, reason);
        const updatedCharacter = db.updateCharacter(character_id, { experience: (char.experience || 0) + amount });

        if (!updatedCharacter) {
          return { content: [{ type: 'text', text: '❌ Failed to update character after awarding XP' }], isError: true };
        }

        return {
          content: [{
            type: 'text',
            text: `✅ Awarded ${amount} XP to '${updatedCharacter.name}'. Reason: ${reason}\n\nTotal XP: ${updatedCharacter.experience}`
          }]
        };
      } catch (e: any) {
        return { content: [{ type: 'text', text: `❌ Failed to award XP: ${e.message}` }], isError: true };
      }
    }

    case 'spend_xp': {
      const { character_id, amount, reason, trait_name } = args;

      if (amount <= 0) {
        return { content: [{ type: 'text', text: '❌ Amount must be positive.' }], isError: true };
      }

      const char = db.getCharacterById(character_id);
      if (!char) {
        return { content: [{ type: 'text', text: '❌ Character not found!' }], isError: true };
      }

      // Use atomic resource operation for XP spending to prevent race conditions
      const result = db.atomicResourceOperation(character_id, 'experience', 'spend', amount);

      if (!result.success) {
        return { content: [{ type: 'text', text: `❌ ${result.error}` }], isError: true };
      }

      // Insert negative amount into ledger after successful spend
      try {
        db.addExperienceLedgerEntry(character_id, -amount, `[SPEND] ${reason}`);
        // Placeholder for trait improvement logic
        // e.g., db.improveTrait(character_id, trait_name, trait_info)
        return {
          content: [{
            type: 'text',
            text: `🟣 ${char.name} spent ${amount} XP. Reason: ${reason}\nAffected trait: ${trait_name || '[none specified]'}\nTotal XP: ${result.newValue}`
          }]
        };
      } catch (e: any) {
        return { content: [{ type: 'text', text: `❌ Failed to record XP spend in ledger: ${e.message}` }], isError: true };
      }
    }

    case 'get_trait_improvement_cost': {
      const { character_id, trait_type, trait_name } = args;

      // Input validation
      if (character_id === undefined || character_id === null) {
        return { content: [{ type: 'text', text: '❌ Error: Missing required field: character_id' }], isError: true };
      }

      if (typeof character_id !== 'number' || !Number.isInteger(character_id) || character_id <= 0) {
        return { content: [{ type: 'text', text: '❌ Error: character_id must be a positive integer' }], isError: true };
      }

      if (!trait_type || typeof trait_type !== 'string') {
        return { content: [{ type: 'text', text: '❌ Error: Missing or invalid trait_type' }], isError: true };
      }

      const validTraitTypes = ['attribute', 'ability', 'discipline', 'sphere', 'art', 'realm', 'willpower', 'power_stat'];
      if (!validTraitTypes.includes(trait_type)) {
        return { content: [{ type: 'text', text: `❌ Error: trait_type must be one of: ${validTraitTypes.join(', ')}` }], isError: true };
      }

      if (!trait_name || typeof trait_name !== 'string') {
        return { content: [{ type: 'text', text: '❌ Error: Missing or invalid trait_name' }], isError: true };
      }

      try {
        const char = db.getCharacterById(character_id);
        if (!char) {
          return { content: [{ type: 'text', text: '❌ Character not found!' }], isError: true };
        }

        // Get current trait value
        let curr_rating = 0;
        let trait_found = false;

        switch (trait_type) {
          case 'attribute':
            const attributeFields = ['strength', 'dexterity', 'stamina', 'charisma', 'manipulation', 'appearance', 'perception', 'intelligence', 'wits'];
            if (attributeFields.includes(trait_name)) {
              curr_rating = char[trait_name] || 1;
              trait_found = true;
            }
            break;
          case 'willpower':
            if (trait_name === 'willpower') {
              curr_rating = char.willpower_permanent || 1;
              trait_found = true;
            }
            break;
          case 'ability':
          case 'discipline':
          case 'sphere':
          case 'art':
          case 'realm':
            // These would need to be looked up in related tables, but for now assume 0 if not found
            curr_rating = 0;
            trait_found = true;
            break;
          case 'power_stat':
            // Game line specific power stats
            switch (char.game_line) {
              case 'vampire':
                if (trait_name === 'generation') {
                  curr_rating = char.generation || 13;
                  trait_found = true;
                }
                break;
              case 'werewolf':
                if (trait_name === 'gnosis') {
                  curr_rating = char.gnosis_permanent || 1;
                  trait_found = true;
                } else if (trait_name === 'rage') {
                  curr_rating = char.rage_permanent || 1;
                  trait_found = true;
                }
                break;
              case 'mage':
                if (trait_name === 'arete') {
                  curr_rating = char.arete || 1;
                  trait_found = true;
                }
                break;
              case 'changeling':
                if (trait_name === 'glamour') {
                  curr_rating = char.glamour_permanent || 1;
                  trait_found = true;
                }
                break;
            }
            break;
        }

        if (!trait_found) {
          return { content: [{ type: 'text', text: `❌ Trait '${trait_name}' not found or not valid for trait_type '${trait_type}' and game_line '${char.game_line}'` }], isError: true };
        }

        // Calculate XP cost for next level
        const new_rating = curr_rating + 1;
        let xp_cost = 0;
        let xp_formula = '';

        switch (trait_type) {
          case 'attribute':
            xp_cost = new_rating * 4;
            xp_formula = 'New rating × 4';
            break;
          case 'ability':
            xp_cost = new_rating * 2;
            xp_formula = 'New rating × 2';
            break;
          case 'discipline':
            xp_cost = new_rating * 5;
            xp_formula = 'New rating × 5';
            break;
          case 'sphere':
          case 'art':
          case 'realm':
            xp_cost = new_rating * 7;
            xp_formula = 'New rating × 7';
            break;
          case 'willpower':
            xp_cost = 8;
            xp_formula = 'Flat 8 XP';
            break;
          case 'power_stat':
            xp_cost = new_rating * 8;
            xp_formula = 'New rating × 8';
            break;
        }

        const hasEnoughXP = (char.experience || 0) >= xp_cost;
        const output = `💰 XP Cost Calculation\n\nTrait: ${trait_name} (${trait_type})\nCurrent Rating: ${curr_rating}\nNew Rating: ${new_rating}\nXP Cost: ${xp_cost} (${xp_formula})\nCharacter XP: ${char.experience || 0}\nCan Afford: ${hasEnoughXP ? '✅ Yes' : '❌ No'}`;

        return {
          content: [
            { type: 'text', text: output },
            { type: 'text', text: JSON.stringify({
              character_id,
              trait_type,
              trait_name,
              current_rating: curr_rating,
              new_rating,
              xp_cost,
              character_xp: char.experience || 0,
              can_afford: hasEnoughXP
            })}
          ]
        };
      } catch (error: any) {
        return { content: [{ type: 'text', text: `❌ Error calculating trait cost: ${error.message}` }], isError: true };
      }
    }

    // --- Inventory Management ---
    case 'add_item': {
      const { character_id, item } = args;
      const char = db.getCharacterById(character_id);
      if (!char) {
        return { content: [{ type: 'text', text: '❌ Character not found!' }], isError: true };
      }

      const newItem = db.addItem(character_id, item);
      return {
        content: [
          { type: 'text', text: `✅ Added '${item.name}' to ${char.name}'s inventory.` },
          { type: 'text', text: JSON.stringify(newItem) }
        ]
      };
    }

    case 'get_inventory': {
      const char = db.getCharacterById(args.character_id);
      if (!char) {
        return { content: [{ type: 'text', text: '❌ Character not found!' }], isError: true };
      }

      const inventory = db.getInventory(args.character_id);
      const output = `🎒 Inventory for ${char.name}:\n` +
        (inventory.length > 0
          ? inventory.map((item: any) => `- ${item.item_name} (x${item.quantity}) [ID: ${item.id}]`).join('\n')
          : '  (Empty)');
      return { content: [{ type: 'text', text: output }] };
    }

    case 'update_item': {
      db.updateItem(args.item_id, args.updates);
      return { content: [{ type: 'text', text: `✅ Item ${args.item_id} updated successfully.` }] };
    }

    case 'remove_item': {
      const success = db.removeItem(args.item_id);
      if (success) {
        return { content: [{ type: 'text', text: `✅ Item ${args.item_id} removed successfully.` }] };
      } else {
        return { content: [{ type: 'text', text: `❌ Item ${args.item_id} not found.` }], isError: true };
      }
    }

    // --- World & Story Persistence ---
    case 'save_world_state': {
      // const { location, notes, data } = args;
      // db.saveWorldState({ location, notes, data });
      return { content: [{ type: 'text', text: "Save world state: unavailable (DB disabled)." }] };
    }
    case 'get_world_state': {
      // const state = db.getWorldState();
      return { content: [{ type: 'text', text: "Get world state: unavailable (DB disabled)." }] };
    }
    case 'save_story_progress': {
      const { chapter } = args;
      if (!chapter) {
        return { content: [{ type: 'text', text: `❌ Chapter is required.` }], isError: true };
      }
      // db.saveStoryProgress({ chapter, scene, summary });
      return { content: [{ type: 'text', text: "Save story progress: unavailable (DB disabled)." }] };
    }
    case 'create_antagonist': {
      const { template_name, custom_name } = args;
      const antagonist = db.createAntagonist(template_name, custom_name) as AntagonistRow;
      if (!antagonist) throw new Error("Antagonist creation failed in database.");
      return {
        content: [
          { type: 'text', text: `✅ Antagonist '${antagonist.name}' created with ID ${antagonist.id}` },
          { type: 'text', text: JSON.stringify({ npc_id: antagonist.id, name: antagonist.name }) }
        ]
      };
    }

    case 'get_antagonist': {
      const antagonist = db.getAntagonistById(args.npc_id);
      if (!antagonist) {
        return { content: [{ type: 'text', text: `❌ Antagonist with ID ${args.npc_id} not found.` }], isError: true };
      }
      return {
        content: [
          { type: 'text', text: `🧛 Antagonist: ${antagonist.name} (${antagonist.template})` },
          { type: 'text', text: JSON.stringify(antagonist) }
        ]
      };
    }

    // --- Antagonist & Character Management ---
    case 'update_antagonist': {
      // db.updateAntagonist(args.npc_id, args.updates);
      return { content: [{ type: 'text', text: "Update antagonist: unavailable (DB disabled)." }] };
    }
    case 'list_antagonists': {
      // const npcs = db.listAntagonists();
      // const output = ...
      return { content: [{ type: 'text', text: "List antagonists: unavailable (DB disabled)." }] };
    }
    case 'remove_antagonist': {
      // const success = db.removeAntagonist(args.npc_id);
      return { content: [{ type: 'text', text: "Remove antagonist: unavailable (DB disabled)." }] };
    }
    case 'list_characters': {
      const characters = db.listCharacters();
      if (characters.length === 0) {
        return { content: [{ type: 'text', text: '📋 No characters found.' }] };
      }

      const output = `📋 Characters (${characters.length}):\n` +
        characters.map((char: any) =>
          `- ${char.name} (${char.game_line}) [ID: ${char.id}]`
        ).join('\n');

      return { content: [{ type: 'text', text: output }] };
    }

    case 'list_antagonists': {
      const npcs = db.listAntagonists();
      if (npcs.length === 0) {
        return { content: [{ type: 'text', text: '👹 No antagonists found.' }] };
      }

      const output = `👹 Antagonists (${npcs.length}):\n` +
        npcs.map((npc: any) =>
          `- ${npc.name} (${npc.template || npc.game_line}) [ID: ${npc.id}]`
        ).join('\n');

      return { content: [{ type: 'text', text: output }] };
    }
    // ---- STATUS EFFECTS SYSTEM ----
    case 'apply_status_effect': {
      const { target_type, target_id, effect_name, description = '', mechanical_effect = {}, duration_type = 'indefinite', duration_value = null } = args;
      const effectId = db.addStatusEffect({
        target_type,
        target_id,
        effect_name,
        description,
        mechanical_effect,
        duration_type,
        duration_value,
      });
      return {
        content: [
          { type: 'text', text: `🌀 Status effect '${effect_name}' applied to ${target_type} #${target_id} (ID: ${effectId})` },
          { type: 'object', effect_id: effectId, target_type, target_id, effect_name, duration_type, duration_value }
        ]
      };
    }

      case 'remove_status_effect': {
        const { effect_id } = args;
        const ok = db.removeStatusEffect(effect_id);
        if (!ok) {
          return { content: [{ type: 'text', text: `❌ Status effect ID ${effect_id} not found.` }], isError: true };
        }
        return { content: [{ type: 'text', text: `✅ Status effect ${effect_id} removed.` }] };
      }

      case 'get_status_effects': {
        const { target_type, target_id } = args;
        const effects = db.listStatusEffects(target_type, target_id);
        return {
          content: [
            { type: 'object', target_type, target_id, effects }
          ]
        };
      }
      case "roll_virtue_check": {
        const { character_id, virtue_name, difficulty } = args;
        return {
          content: makeTextContentArray([
            {
              description:
                "Delegating to combat-engine-server. Please call roll_wod_pool there.",
              next_tool_call: {
                server: "rpg-combat-engine-server",
                tool_name: "roll_wod_pool",
                arguments: { character_id, virtue_name, difficulty, pool_size: 3 }
              }
            }
          ])
        };
      }
    } // end switch
    console.log(`Inside try block, after switch statement: ${name}`);
  } // close try
  catch (e: any) {
    console.error(`Error in handleToolRequest: ${e.message || String(e)}`);
    return { content: [{ type: 'text', text: `❌ Error: ${e.message || String(e)}` }], isError: true };
    } // end catch
}

console.log("Registering CallToolRequestSchema handler...");
server.setRequestHandler(CallToolRequestSchema, handleToolRequest as any);

console.log("Initializing transport...");
const stdioTransport = new StdioServerTransport();
console.log("Transport initialized.");

// Add general request logging
server.onerror = (error) => {
  console.error("Server error:", error);
};

server.connect(stdioTransport);
console.error('oWoD RPG Game State MCP Server v2.1.0 running on stdio');
